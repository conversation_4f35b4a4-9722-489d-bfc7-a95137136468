<?php
session_start();
include 'includes/config/database.php';
include 'includes/functions/utility.php';

// Redirect to resident portal by default
if (!isset($_SESSION['user_id'])) {
    header("Location: resident_portal/index.php");
    exit;
}

// Check for database connection errors
$db_error = isset($db_connection_error) && $db_connection_error;

// Get user information
$user_id = $_SESSION['user_id'];
$user = array(
    'username' => isset($_SESSION['username']) ? $_SESSION['username'] : 'User',
    'user_type' => isset($_SESSION['user_type']) ? $_SESSION['user_type'] : 'Guest'
);

// Initialize counters
$resident_count = 0;
$household_count = 0;
$senior_count = 0;
$pwd_count = 0;
$pending_complaints = 0;
$document_requests = 0;
$active_disasters = 0;
$pending_assistance = 0;
$financial_balance = 0;
$upcoming_events = 0;
$bail_violations = 0;

// Function to check if a table exists
function table_exists($conn, $table) {
    try {
        $result = $conn->query("SHOW TABLES LIKE '$table'");
        return $result && $result->rowCount() > 0;
    } catch(PDOException $e) {
        error_log("Error checking if table exists: " . $e->getMessage());
        return false;
    }
}

if (!$db_error) {
    // Get user data
    if (table_exists($conn, 'users')) {
        $query = "SELECT * FROM users WHERE user_id = :user_id";
        $stmt = $conn->prepare($query);
        $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
        $stmt->execute();
        if ($stmt->rowCount() > 0) {
            $user = $stmt->fetch(PDO::FETCH_ASSOC);
        }
    }

    // Get counts for dashboard
    // Resident count
    if (table_exists($conn, 'residents')) {
        $query = "SELECT COUNT(*) as total FROM residents WHERE status = 'Active'";
        $stmt = $conn->query($query);
        if ($stmt) {
            $row = $stmt->fetch(PDO::FETCH_ASSOC);
            $resident_count = $row['total'];
        }
    }

    // Household count
    if (table_exists($conn, 'households')) {
        $query = "SELECT COUNT(*) as total FROM households";
        $stmt = $conn->query($query);
        if ($stmt) {
            $row = $stmt->fetch(PDO::FETCH_ASSOC);
            $household_count = $row['total'];
        }
    }

    // Senior citizen count
    if (table_exists($conn, 'senior_citizens')) {
        $query = "SELECT COUNT(*) as total FROM senior_citizens WHERE status = 'Active'";
        $stmt = $conn->query($query);
        if ($stmt) {
            $row = $stmt->fetch(PDO::FETCH_ASSOC);
            $senior_count = $row['total'];
        }
    } else if (table_exists($conn, 'residents')) {
        // Check in residents table if separate senior_citizens table doesn't exist
        $query = "SELECT COUNT(*) as total FROM residents WHERE is_senior = 1 AND status = 'Active'";
        $stmt = $conn->query($query);
        if ($stmt) {
            $row = $stmt->fetch(PDO::FETCH_ASSOC);
            $senior_count = $row['total'];
        }
    }

    // PWD count
    if (table_exists($conn, 'pwd')) {
        $query = "SELECT COUNT(*) as total FROM pwd WHERE status = 'Active'";
        $stmt = $conn->query($query);
        if ($stmt) {
            $row = $stmt->fetch(PDO::FETCH_ASSOC);
            $pwd_count = $row['total'];
        }
    } else if (table_exists($conn, 'residents')) {
        // Check in residents table if separate pwd table doesn't exist
        $query = "SELECT COUNT(*) as total FROM residents WHERE is_pwd = 1 AND status = 'Active'";
        $stmt = $conn->query($query);
        if ($stmt) {
            $row = $stmt->fetch(PDO::FETCH_ASSOC);
            $pwd_count = $row['total'];
        }
    }

    // Pending complaints
    if (table_exists($conn, 'complaints')) {
        $query = "SELECT COUNT(*) as total FROM complaints WHERE status = 'Pending' OR status = 'Under Investigation'";
        $stmt = $conn->query($query);
        if ($stmt) {
            $row = $stmt->fetch(PDO::FETCH_ASSOC);
            $pending_complaints = $row['total'];
        }
    }

    // Document requests
    if (table_exists($conn, 'document_requests')) {
        // Check payment_status field - if it exists, we need to handle it differently
        $has_payment_status = false;
        try {
            $check_field = $conn->query("SHOW COLUMNS FROM document_requests LIKE 'payment_status'");
            $has_payment_status = $check_field && $check_field->rowCount() > 0;
        } catch (PDOException $e) {
            // Field doesn't exist, continue with normal query
        }

        if ($has_payment_status) {
            // Include documents that are not yet released/cancelled/rejected
            // Make sure we handle case-sensitivity in status
            $query = "SELECT COUNT(*) as total FROM document_requests
                      WHERE LOWER(status) NOT IN ('released', 'cancelled', 'rejected')";
        } else {
            $query = "SELECT COUNT(*) as total FROM document_requests
                      WHERE LOWER(status) IN ('pending', 'processing', 'ready for pickup', 'approved', 'paid')";
        }

        $stmt = $conn->query($query);
        if ($stmt) {
            $row = $stmt->fetch(PDO::FETCH_ASSOC);
            $document_requests = $row['total'];
        }
    } else if (table_exists($conn, 'documents')) {
        // Try to include all document statuses except those that indicate completion or rejection
        $query = "SELECT COUNT(*) as total FROM documents
                  WHERE LOWER(status) NOT IN ('released', 'rejected', 'cancelled')";
        $stmt = $conn->query($query);
        if ($stmt) {
            $row = $stmt->fetch(PDO::FETCH_ASSOC);
            $document_requests = $row['total'];
        }
    }

    // Active disasters
    if (table_exists($conn, 'disaster_management')) {
        $query = "SELECT COUNT(*) as total FROM disaster_management WHERE status != 'Completed'";
        $stmt = $conn->query($query);
        if ($stmt) {
            $row = $stmt->fetch(PDO::FETCH_ASSOC);
            $active_disasters = $row['total'];
        }
    }

    // Assistance Programs
    if (table_exists($conn, 'assistance_programs')) {
        $query = "SELECT COUNT(*) as total FROM assistance_programs";
        $stmt = $conn->query($query);
        if ($stmt) {
            $row = $stmt->fetch(PDO::FETCH_ASSOC);
            $pending_assistance = $row['total'];
        }
    } else if (table_exists($conn, 'assistance')) {
        // Check in assistance table as an alternative
        $query = "SELECT COUNT(*) as total FROM assistance";
        $stmt = $conn->query($query);
        if ($stmt) {
            $row = $stmt->fetch(PDO::FETCH_ASSOC);
            $pending_assistance = $row['total'];
        }
    } else if (table_exists($conn, 'aid_requests')) {
        // Another possible alternative table name
        $query = "SELECT COUNT(*) as total FROM aid_requests";
        $stmt = $conn->query($query);
        if ($stmt) {
            $row = $stmt->fetch(PDO::FETCH_ASSOC);
            $pending_assistance = $row['total'];
        }
    }

    // Bail violations (Pending Cases)
    if (table_exists($conn, 'bail_violations')) {
        $query = "SELECT COUNT(*) as total FROM bail_violations WHERE status IN ('Reported', 'In Process')";
        $stmt = $conn->query($query);
        if ($stmt) {
            $row = $stmt->fetch(PDO::FETCH_ASSOC);
            $bail_violations = $row['total'];
        }
    }

    // Financial balance
    if (table_exists($conn, 'finances')) {
        $query = "SELECT
                    SUM(CASE WHEN transaction_type = 'Income' THEN amount ELSE 0 END) as total_income,
                    SUM(CASE WHEN transaction_type = 'Expense' THEN amount ELSE 0 END) as total_expenses
                  FROM finances WHERE status = 'Approved'";
        $stmt = $conn->query($query);
        if ($stmt) {
            $row = $stmt->fetch(PDO::FETCH_ASSOC);
            $financial_balance = ($row['total_income'] ?: 0) - ($row['total_expenses'] ?: 0);
        }
    }

    // Upcoming events
    if (table_exists($conn, 'announcements')) {
        // First check if any events exist at all - a more permissive query
        $count_query = "SELECT COUNT(*) as total FROM announcements
                        WHERE announcement_type = 'Event' AND status = 'Active'";
        $count_stmt = $conn->query($count_query);
        if ($count_stmt && $count_stmt->fetchColumn() > 0) {
            $upcoming_events = 1; // At minimum, show 1 if any active events exist

            // Try with start_date field
            $start_date_check = $conn->query("SHOW COLUMNS FROM announcements LIKE 'start_date'");
            if ($start_date_check && $start_date_check->rowCount() > 0) {
                $future_query = "SELECT COUNT(*) as total FROM announcements
                          WHERE announcement_type = 'Event'
                          AND status = 'Active'
                          AND start_date >= CURDATE()";
                $future_stmt = $conn->query($future_query);
                if ($future_stmt && $future_stmt->fetchColumn() > 0) {
                    // If we have future events, update the count
                    $query = "SELECT COUNT(*) as total FROM announcements
                              WHERE announcement_type = 'Event'
                              AND status = 'Active'
                              AND start_date >= CURDATE()";
                    $stmt = $conn->query($query);
                    if ($stmt) {
                        $row = $stmt->fetch(PDO::FETCH_ASSOC);
                        $upcoming_events = $row['total'];
                    }
                }
                // Otherwise keep the minimum count of 1
            } else {
                // Try with event_date if start_date doesn't exist
                $event_date_check = $conn->query("SHOW COLUMNS FROM announcements LIKE 'event_date'");
                if ($event_date_check && $event_date_check->rowCount() > 0) {
                    $future_query = "SELECT COUNT(*) as total FROM announcements
                              WHERE announcement_type = 'Event'
                              AND status = 'Active'
                              AND event_date >= CURDATE()";
                    $future_stmt = $conn->query($future_query);
                    if ($future_stmt && $future_stmt->fetchColumn() > 0) {
                        // If we have future events, update the count
                        $query = "SELECT COUNT(*) as total FROM announcements
                                  WHERE announcement_type = 'Event'
                                  AND status = 'Active'
                                  AND event_date >= CURDATE()";
                        $stmt = $conn->query($query);
                        if ($stmt) {
                            $row = $stmt->fetch(PDO::FETCH_ASSOC);
                            $upcoming_events = $row['total'];
                        }
                    }
                    // Otherwise keep the minimum count of 1
                }
            }
        }
    } else if (table_exists($conn, 'events')) {
        // Check events table if it exists as an alternative
        $query = "SELECT COUNT(*) as total FROM events WHERE status = 'Active'";
        $stmt = $conn->query($query);
        if ($stmt) {
            $row = $stmt->fetch(PDO::FETCH_ASSOC);
            $upcoming_events = $row['total'];
        }
    }
}

// Get recent announcements
$announcements = array();
if (!$db_error && table_exists($conn, 'announcements')) {
    $query = "SELECT * FROM announcements
              WHERE status = 'Active'
              ORDER BY date_posted DESC
              LIMIT 5";
    $stmt = $conn->query($query);
    if ($stmt) {
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            $announcements[] = $row;
        }
    }
}

// Get recent activities with enhanced details
$activities = array();
if (!$db_error && table_exists($conn, 'activity_logs') && table_exists($conn, 'users')) {
    try {
        $query = "SELECT a.*,
                         u.username,
                         u.user_type,
                         CASE
                             WHEN a.action_type LIKE '%Certificate%' OR a.action_type LIKE '%Print%' THEN '🖨️'
                             WHEN a.action_type LIKE '%Login%' THEN '🔐'
                             WHEN a.action_type LIKE '%Add%' OR a.action_type LIKE '%Create%' THEN '➕'
                             WHEN a.action_type LIKE '%Edit%' OR a.action_type LIKE '%Update%' THEN '✏️'
                             WHEN a.action_type LIKE '%Delete%' THEN '🗑️'
                             WHEN a.action_type LIKE '%Document%' THEN '📄'
                             WHEN a.action_type LIKE '%Backup%' THEN '💾'
                             ELSE '📋'
                         END as activity_icon
                  FROM activity_logs a
                  LEFT JOIN users u ON a.user_id = u.user_id
                  ORDER BY a.action_timestamp DESC
                  LIMIT 15";
        $stmt = $conn->prepare($query);
        $stmt->execute();
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            $activities[] = $row;
        }
    } catch (PDOException $e) {
        error_log("Error fetching activities: " . $e->getMessage());
    }
}

// Page title
$page_title = "Dashboard - Barangay Management System";

// Get barangay name from settings
$barangay_name = get_setting($conn, 'barangay_name', 'Barangay');
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>

    <!-- Favicon -->
    <link rel="icon" href="<?php echo get_favicon_url($conn, ''); ?>" type="image/png">

    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/chart.js@4.2.1/dist/chart.umd.min.js">
    <link rel="stylesheet" href="assets/css/style.css">
    <style>
        .stat-card {
            border-radius: 0.75rem;
            box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15) !important;
            transition: all 0.3s ease;
            border: none;
            position: relative;
            z-index: 1;
        }
        .stat-card:hover {
            transform: translateY(-5px) !important;
            box-shadow: 0 0.5rem 2rem 0 rgba(58, 59, 69, 0.2) !important;
            z-index: 2;
        }
        .stat-icon {
            font-size: 2.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            height: 60px;
            width: 60px;
            border-radius: 50%;
            margin-bottom: 10px;
            transition: transform 0.3s ease;
        }
        .stat-card:hover .stat-icon {
            transform: scale(1.1);
        }
        .chart-container {
            position: relative;
            height: 300px;
            margin-bottom: 20px;
        }
        @media (max-width: 768px) {
            .chart-container {
                height: 250px;
            }
        }

        /* Background colors with opacity */
        .bg-primary-soft { background-color: rgba(78, 115, 223, 0.1) !important; }
        .bg-success-soft { background-color: rgba(28, 200, 138, 0.1) !important; }
        .bg-info-soft { background-color: rgba(54, 185, 204, 0.1) !important; }
        .bg-warning-soft { background-color: rgba(246, 194, 62, 0.1) !important; }
        .bg-danger-soft { background-color: rgba(231, 74, 59, 0.1) !important; }
        .bg-secondary-soft { background-color: rgba(108, 117, 125, 0.1) !important; }

        .custom-toast {
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            opacity: 0.95;
            min-width: 280px;
            border: none;
            overflow: hidden;
        }

        .custom-toast .toast-header {
            background-color: rgba(255, 255, 255, 0.9);
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            padding: 0.75rem 1rem;
        }

        .custom-toast .toast-body {
            padding: 1rem;
            font-weight: 500;
        }

        /* Toast color variations */
        .toast.bg-success {
            background: linear-gradient(45deg, #28a745, #20c997) !important;
        }

        .toast.bg-danger {
            background: linear-gradient(45deg, #dc3545, #ff6b6b) !important;
        }

        .toast.bg-warning {
            background: linear-gradient(45deg, #ffc107, #ffda79) !important;
            color: #333;
        }

        .toast.bg-info {
            background: linear-gradient(45deg, #17a2b8, #66d9e8) !important;
            color: white;
        }

        /* Activity List Styling */
        .activity-list {
            max-height: 500px;
            overflow-y: auto;
        }

        .activity-item {
            transition: background-color 0.2s ease;
        }

        .activity-item:hover {
            background-color: #f8f9fa;
        }

        .activity-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(45deg, #667eea, #764ba2);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            color: white;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .activity-content {
            font-size: 14px;
        }

        .activity-content strong {
            color: #495057;
            font-weight: 600;
        }

        .activity-list::-webkit-scrollbar {
            width: 6px;
        }

        .activity-list::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 3px;
        }

        .activity-list::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 3px;
        }

        .activity-list::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <?php include 'includes/sidebar.php'; ?>

            <!-- Main Content -->
            <main class="col-md-8 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">Dashboard</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <!-- Export button removed as per latest user request -->
                        </div>
                        
                    </div>
                </div>

                <?php if ($db_error): ?>
                <div class="alert alert-danger">
                    <strong>Database Error:</strong> Could not connect to the database. Please contact the system administrator.
                </div>
                <?php else: ?>

                <!-- Welcome Card -->
                <a href="admin/users.php" class="text-decoration-none">
                    <div class="card mb-4 border-left-primary shadow stat-card">
                        <div class="card-body">
                            <div class="row align-items-center">
                                <div class="col-xl-9">
                                    <h4 class="text-primary">Welcome <?php echo isset($_SESSION['welcome_name']) ? $_SESSION['welcome_name'] : 'Talisaynon'; ?>!</h4>
                                    <p class="mb-0">Hello, <?php echo $user['username']; ?>! You are logged in as <?php echo $user['user_type']; ?>.</p>
                                </div>
                                <div class="col-xl-3 text-end">
                                    <i class="fas fa-home stat-icon text-primary"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </a>

                <!-- Main Statistics -->
                <div class="row">
                    <!-- Population Stats -->
                    <div class="col-lg-3 col-md-6 mb-4">
                        <a href="modules/residents/residents.php" class="text-decoration-none">
                            <div class="card stat-card shadow h-100">
                                <div class="card-body p-3">
                                    <div class="row align-items-center">
                                        <div class="col-4">
                                            <div class="stat-icon bg-primary-soft text-primary">
                                                👥
                                            </div>
                                        </div>
                                        <div class="col-8 text-end">
                                            <h4 class="mt-0 mb-1"><?php echo $resident_count; ?></h4>
                                            <p class="mb-0 text-muted">Residents</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </a>
                    </div>

                    <!-- Household Stats -->
                    <div class="col-lg-3 col-md-6 mb-4">
                        <a href="modules/residents/households.php" class="text-decoration-none">
                            <div class="card stat-card shadow h-100">
                                <div class="card-body p-3">
                                    <div class="row align-items-center">
                                        <div class="col-4">
                                            <div class="stat-icon bg-success-soft text-success">
                                                🏠
                                            </div>
                                        </div>
                                        <div class="col-8 text-end">
                                            <h4 class="mt-0 mb-1"><?php echo $household_count; ?></h4>
                                            <p class="mb-0 text-muted">Households</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </a>
                    </div>

                    <!-- Senior Citizens Stats -->
                    <div class="col-lg-3 col-md-6 mb-4">
                        <a href="modules/social/seniors.php" class="text-decoration-none">
                            <div class="card stat-card shadow h-100">
                                <div class="card-body p-3">
                                    <div class="row align-items-center">
                                        <div class="col-4">
                                            <div class="stat-icon bg-info-soft text-info">
                                                👴
                                            </div>
                                        </div>
                                        <div class="col-8 text-end">
                                            <h4 class="mt-0 mb-1"><?php echo $senior_count; ?></h4>
                                            <p class="mb-0 text-muted">Senior Citizens</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </a>
                    </div>

                    <!-- PWD Stats -->
                    <div class="col-lg-3 col-md-6 mb-4">
                        <a href="modules/social/pwd.php" class="text-decoration-none">
                            <div class="card stat-card shadow h-100">
                                <div class="card-body p-3">
                                    <div class="row align-items-center">
                                        <div class="col-4">
                                            <div class="stat-icon bg-warning-soft text-warning">
                                                ♿
                                            </div>
                                        </div>
                                        <div class="col-8 text-end">
                                            <h4 class="mt-0 mb-1"><?php echo $pwd_count; ?></h4>
                                            <p class="mb-0 text-muted">PWD</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </a>
                    </div>
                </div>

                <!-- Services Stats -->
                <div class="row mb-4">
                    <!-- Pending Complaints -->
                    <div class="col-lg-3 col-md-6 mb-4">
                        <a href="modules/complaints/complaints.php" class="text-decoration-none">
                            <div class="card stat-card shadow h-100">
                                <div class="card-body p-3">
                                    <div class="row align-items-center">
                                        <div class="col-4">
                                            <div class="stat-icon bg-danger-soft text-danger">
                                                📝
                                            </div>
                                        </div>
                                        <div class="col-8 text-end">
                                            <h4 class="mt-0 mb-1"><?php echo $pending_complaints; ?></h4>
                                            <p class="mb-0 text-muted">Pending Complaints</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </a>
                    </div>

                    <!-- Pending Cases -->
                    <div class="col-lg-3 col-md-6 mb-4">
                        <a href="modules/bail/dashboard.php" class="text-decoration-none">
                            <div class="card stat-card shadow h-100">
                                <div class="card-body p-3">
                                    <div class="row align-items-center">
                                        <div class="col-4">
                                            <div class="stat-icon bg-warning-soft text-warning">
                                                ⚖️
                                            </div>
                                        </div>
                                        <div class="col-8 text-end">
                                            <h4 class="mt-0 mb-1"><?php echo $bail_violations; ?></h4>
                                            <p class="mb-0 text-muted">Pending Cases</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </a>
                    </div>

                    <!-- Document Requests -->
                    <div class="col-lg-3 col-md-6 mb-4">
                        <a href="modules/documents/documents.php" class="text-decoration-none">
                            <div class="card stat-card shadow h-100">
                                <div class="card-body p-3">
                                    <div class="row align-items-center">
                                        <div class="col-4">
                                            <div class="stat-icon bg-info-soft text-info">
                                                📄
                                            </div>
                                        </div>
                                        <div class="col-8 text-end">
                                            <h4 class="mt-0 mb-1"><?php echo $document_requests; ?></h4>
                                            <p class="mb-0 text-muted">Document Requests</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </a>
                    </div>

                    <!-- Active Disasters -->
                    <div class="col-lg-3 col-md-6 mb-4">
                        <a href="modules/disaster/disaster_preparedness.php" class="text-decoration-none">
                            <div class="card stat-card shadow h-100">
                                <div class="card-body p-3">
                                    <div class="row align-items-center">
                                        <div class="col-4">
                                            <div class="stat-icon bg-danger-soft text-danger">
                                                🚨
                                            </div>
                                        </div>
                                        <div class="col-8 text-end">
                                            <h4 class="mt-0 mb-1"><?php echo $active_disasters; ?></h4>
                                            <p class="mb-0 text-muted">Active Disasters</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </a>
                    </div>
                </div>

                <!-- Additional Stats Row -->
                <div class="row mb-4">
                    <!-- Assistance Programs -->
                    <div class="col-lg-3 col-md-6 mb-4">
                        <a href="modules/assistance/programs.php" class="text-decoration-none">
                            <div class="card stat-card shadow h-100">
                                <div class="card-body p-3">
                                    <div class="row align-items-center">
                                        <div class="col-4">
                                            <div class="stat-icon bg-primary-soft text-primary">
                                                🤝
                                            </div>
                                        </div>
                                        <div class="col-8 text-end">
                                            <h4 class="mt-0 mb-1"><?php echo $pending_assistance; ?></h4>
                                            <p class="mb-0 text-muted">Assistance Programs</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </a>
                    </div>

                    <!-- Financial Balance -->
                    <div class="col-lg-3 col-md-6 mb-4">
                        <a href="modules/finance/budget.php" class="text-decoration-none">
                            <div class="card stat-card shadow h-100">
                                <div class="card-body p-3">
                                    <div class="row align-items-center">
                                        <div class="col-4">
                                            <div class="stat-icon bg-success-soft text-success">
                                                💰
                                            </div>
                                        </div>
                                        <div class="col-8 text-end">
                                            <h4 class="mt-0 mb-1">₱<?php echo number_format($financial_balance, 2); ?></h4>
                                            <p class="mb-0 text-muted">Financial Balance</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </a>
                    </div>

                    <!-- Upcoming Events -->
                    <div class="col-lg-3 col-md-6 mb-4">
                        <a href="modules/announcements/announcements.php?type=Event" class="text-decoration-none">
                            <div class="card stat-card shadow h-100">
                                <div class="card-body p-3">
                                    <div class="row align-items-center">
                                        <div class="col-4">
                                            <div class="stat-icon bg-info-soft text-info">
                                                📅
                                            </div>
                                        </div>
                                        <div class="col-8 text-end">
                                            <h4 class="mt-0 mb-1"><?php echo $upcoming_events; ?></h4>
                                            <p class="mb-0 text-muted">Upcoming Events</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </a>
                    </div>

                    <!-- Date Today -->
                    <div class="col-lg-3 col-md-6 mb-4">
                            <div class="card stat-card shadow h-100">
                                <div class="card-body p-3">
                                    <div class="row align-items-center">
                                        <div class="col-4">
                                            <div class="stat-icon bg-secondary-soft text-secondary">
                                                📅
                                            </div>
                                        </div>
                                        <div class="col-8 text-end">
                                            <h4 class="mt-0 mb-1"><?php echo date('F d, Y'); ?></h4>
                                            <p class="mb-0 text-muted">Date Today</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </a>
                    </div>
                </div>

                <!-- Recent Activities and Announcements -->
                <div class="row mt-4">
                    <!-- Recent Activities -->
                    <div class="col-lg-8">
                        <div class="card stat-card shadow mb-4">
                            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                                <h6 class="m-0 font-weight-bold text-primary">
                                    <i class="fas fa-history me-2"></i>Recent Activities
                                </h6>
                                <a href="admin/logs.php" class="btn btn-sm btn-primary">
                                    <i class="fas fa-list me-1"></i>View All
                                </a>
                            </div>
                            <div class="card-body">
                                <?php if (empty($activities)): ?>
                                <div class="text-center py-4">
                                    <i class="fas fa-history fa-3x text-gray-300 mb-3"></i>
                                    <p class="text-muted">No recent activities found.</p>
                                </div>
                                <?php else: ?>
                                <div class="activity-list">
                                    <?php foreach ($activities as $activity): ?>
                                    <div class="activity-item border-bottom py-3">
                                        <div class="row align-items-center">
                                            <div class="col-auto">
                                                <div class="activity-icon">
                                                    <?php echo $activity['activity_icon']; ?>
                                                </div>
                                            </div>
                                            <div class="col">
                                                <div class="activity-content">
                                                    <div class="d-flex justify-content-between align-items-start">
                                                        <div>
                                                            <strong>User:</strong>
                                                            <span class="badge bg-primary me-2">
                                                                <?php echo htmlspecialchars($activity['user_type'] ?? 'User'); ?>
                                                            </span>
                                                            <?php echo htmlspecialchars($activity['username'] ?? 'Unknown'); ?>
                                                        </div>
                                                        <small class="text-muted">
                                                            <?php
                                                            $time_diff = time() - strtotime($activity['action_timestamp']);
                                                            if ($time_diff < 60) {
                                                                echo "Just now";
                                                            } elseif ($time_diff < 3600) {
                                                                echo floor($time_diff / 60) . " min ago";
                                                            } elseif ($time_diff < 86400) {
                                                                echo floor($time_diff / 3600) . " hr ago";
                                                            } else {
                                                                echo date('M d, Y h:i A', strtotime($activity['action_timestamp']));
                                                            }
                                                            ?>
                                                        </small>
                                                    </div>
                                                    <div class="mt-1">
                                                        <strong>Action:</strong>
                                                        <?php
                                                        $action_text = $activity['action_type'] ?? 'Unknown action';
                                                        $action_details = $activity['action_details'] ?? '';

                                                        // Format certificate printing activities
                                                        if (strpos($action_text, 'Certificate') !== false || strpos($action_details, 'Certificate') !== false) {
                                                            echo '<span class="text-success">' . htmlspecialchars($action_text) . '</span>';
                                                        } elseif (strpos($action_text, 'Print') !== false) {
                                                            echo '<span class="text-info">' . htmlspecialchars($action_text) . '</span>';
                                                        } elseif (strpos($action_text, 'Delete') !== false) {
                                                            echo '<span class="text-danger">' . htmlspecialchars($action_text) . '</span>';
                                                        } elseif (strpos($action_text, 'Login') !== false) {
                                                            echo '<span class="text-primary">' . htmlspecialchars($action_text) . '</span>';
                                                        } else {
                                                            echo '<span class="text-dark">' . htmlspecialchars($action_text) . '</span>';
                                                        }
                                                        ?>
                                                    </div>
                                                    <?php if (!empty($action_details)): ?>
                                                    <div class="mt-1">
                                                        <small class="text-muted">
                                                            <?php echo htmlspecialchars(substr($action_details, 0, 100)) . (strlen($action_details) > 100 ? '...' : ''); ?>
                                                        </small>
                                                    </div>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <?php endforeach; ?>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <!-- Announcements -->
                    <div class="col-lg-4">
                        <div class="card stat-card shadow mb-4">
                            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                                <h6 class="m-0 font-weight-bold text-primary">Announcements</h6>
                                <a href="modules/announcements/index.php" class="btn btn-sm btn-primary">View All</a>
                            </div>
                            <div class="card-body">
                                <?php if (empty($announcements)): ?>
                                <div class="text-center py-4">
                                    <i class="fas fa-bullhorn fa-3x text-gray-300 mb-3"></i>
                                    <p class="text-muted">No announcements found.</p>
                                </div>
                                <?php else: ?>
                                <div class="announcement-list">
                                    <?php foreach ($announcements as $announcement): ?>
                                    <div class="announcement-item mb-3">
                                        <h6><?php echo htmlspecialchars($announcement['title']); ?></h6>
                                        <p class="small text-muted mb-2">
                                            <i class="fas fa-calendar-alt me-1"></i>
                                            <?php echo date('M d, Y', strtotime($announcement['date_posted'])); ?>
                                            <?php if ($announcement['announcement_type'] == 'Event'): ?>
                                            <span class="badge bg-info ms-2">Event</span>
                                            <?php endif; ?>
                                        </p>
                                        <p class="mb-0"><?php echo nl2br(htmlspecialchars(substr($announcement['content'], 0, 150) . (strlen($announcement['content']) > 150 ? '...' : ''))); ?></p>
                                    </div>
                                    <?php endforeach; ?>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>

                        <!-- Quick Links -->
                        <div class="card stat-card shadow mb-4">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-primary">Quick Links</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-6 mb-2">
                                        <a href="modules/residents/add_resident.php" class="btn btn-sm btn-block btn-primary w-100">
                                            <i class="fas fa-user-plus"></i> Add Resident
                                        </a>
                                    </div>
                                    <div class="col-6 mb-2">
                                        <a href="modules/documents/documents.php" class="btn btn-sm btn-block btn-success w-100">
                                            <i class="fas fa-file-alt"></i> Document Requests
                                        </a>
                                    </div>
                                    <div class="col-6 mb-2">
                                        <a href="modules/complaints/complaints.php" class="btn btn-sm btn-block btn-danger w-100">
                                            <i class="fas fa-exclamation-circle"></i> Blotter
                                        </a>
                                    </div>
                                    <div class="col-6 mb-2">
                                        <a href="modules/announcements/announcements.php" class="btn btn-sm btn-block btn-info w-100">
                                            <i class="fas fa-bullhorn"></i> Announcements
                                        </a>
                                    </div>
                                    <?php if (isset($_SESSION['user_type']) && $_SESSION['user_type'] == 'Admin'): ?>
                                    <div class="col-12 mb-2">
                                        <a href="admin/dashboard.php" class="btn btn-sm btn-block btn-dark w-100">
                                            <i class="fas fa-tachometer-alt"></i> Admin Dashboard
                                        </a>
                                    </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <?php endif; ?>
            </main>
        </div>
    </div>

    <!-- Toast container for notifications -->
    <div class="position-fixed top-0 end-0 p-3" style="z-index: 11">
        <div id="liveToast" class="toast custom-toast" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="toast-header">
                <i class="fas fa-bell me-2"></i>
                <strong class="me-auto" id="toastTitle">Notification</strong>
                <small id="toastTime">Just now</small>
                <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
            <div class="toast-body" id="toastMessage">
                <!-- Toast message will be placed here -->
            </div>
        </div>
    </div>

    <!-- Logout Modal-->
    <div class="modal fade" id="logoutModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.7.1/dist/chart.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <!-- Toast notification script -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const toastElement = document.getElementById('liveToast');

            <?php if (isset($_SESSION['login_message']) && !empty($_SESSION['login_message'])): ?>
                // Set toast content
                document.getElementById('toastTitle').innerHTML = '<i class="fas fa-check-circle me-1"></i> Login Success';
                document.getElementById('toastMessage').innerHTML = '<?php echo $_SESSION['login_message']; ?>';
                document.getElementById('toastTime').innerHTML = 'Just now';

                // Get the toast element
                toastElement.classList.add('bg-success', 'text-white');
                const loginToast = new bootstrap.Toast(toastElement, {
                    delay: 5000, // Auto-hide after 5 seconds
                    animation: true
                });

                // Show the toast
                loginToast.show();

                <?php
                // Clear the message after showing it
                unset($_SESSION['login_message']);
                ?>
            <?php endif; ?>

            <?php if (isset($_SESSION['message']) && !empty($_SESSION['message'])): ?>
                // Set toast content for general messages
                let iconClass = 'fas fa-info-circle';

                <?php if (isset($_SESSION['message_type'])): ?>
                    switch('<?php echo $_SESSION['message_type']; ?>') {
                        case 'success':
                            iconClass = 'fas fa-check-circle';
                            break;
                        case 'error':
                        case 'danger':
                            iconClass = 'fas fa-exclamation-circle';
                            break;
                        case 'warning':
                            iconClass = 'fas fa-exclamation-triangle';
                            break;
                    }
                <?php endif; ?>

                document.getElementById('toastTitle').innerHTML = '<i class="' + iconClass + ' me-1"></i> <?php echo isset($_SESSION['message_type']) ? ucfirst($_SESSION['message_type']) : "Notification"; ?>';
                document.getElementById('toastMessage').innerHTML = '<?php echo $_SESSION['message']; ?>';

                // Reset previous classes
                toastElement.classList.remove('bg-success', 'bg-danger', 'bg-warning', 'bg-info', 'text-white');

                // Set toast background color based on message type
                <?php if (isset($_SESSION['message_type'])): ?>
                    switch('<?php echo $_SESSION['message_type']; ?>') {
                        case 'success':
                            toastElement.classList.add('bg-success', 'text-white');
                            break;
                        case 'error':
                        case 'danger':
                            toastElement.classList.add('bg-danger', 'text-white');
                            break;
                        case 'warning':
                            toastElement.classList.add('bg-warning');
                            break;
                        case 'info':
                            toastElement.classList.add('bg-info', 'text-white');
                            break;
                    }
                <?php endif; ?>

                const messageToast = new bootstrap.Toast(toastElement, {
                    delay: 5000,
                    animation: true
                });

                // Show the toast
                messageToast.show();

                <?php
                // Clear the message after showing it
                unset($_SESSION['message']);
                unset($_SESSION['message_type']);
                ?>
            <?php endif; ?>
        });

        // Function to show toast programmatically
        function showToast(title, message, type = 'info') {
            let iconClass = 'fas fa-info-circle';

            switch(type) {
                case 'success':
                    iconClass = 'fas fa-check-circle';
                    break;
                case 'error':
                case 'danger':
                    iconClass = 'fas fa-exclamation-circle';
                    break;
                case 'warning':
                    iconClass = 'fas fa-exclamation-triangle';
                    break;
            }

            document.getElementById('toastTitle').innerHTML = '<i class="' + iconClass + ' me-1"></i> ' + title;
            document.getElementById('toastMessage').innerHTML = message;
            document.getElementById('toastTime').innerHTML = 'Just now';

            const toastEl = document.getElementById('liveToast');

            // Reset previous classes
            toastEl.classList.remove('bg-success', 'bg-danger', 'bg-warning', 'bg-info', 'text-white');

            // Add appropriate class based on type
            switch(type) {
                case 'success':
                    toastEl.classList.add('bg-success', 'text-white');
                    break;
                case 'error':
                case 'danger':
                    toastEl.classList.add('bg-danger', 'text-white');
                    break;
                case 'warning':
                    toastEl.classList.add('bg-warning');
                    break;
                case 'info':
                    toastEl.classList.add('bg-info', 'text-white');
                    break;
            }

            const toast = new bootstrap.Toast(toastEl, {
                delay: 5000,
                animation: true
            });
            toast.show();
        }
    </script>
</body>
</html>