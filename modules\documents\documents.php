<?php
session_start();
include '../../includes/config/database.php';
include '../../includes/functions/utility.php';

// Clear any lingering error messages if this is a fresh page load
if (!isset($_SERVER['HTTP_X_REQUESTED_WITH']) || strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) != 'xmlhttprequest') {
    if (isset($_SESSION['error'])) {
        unset($_SESSION['error']);
    }
}

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header("Location: ../../login.php");
    exit;
}

// Check if database connection has an error
$db_error = isset($db_connection_error) && $db_connection_error;

// Default values
$total_records = 0;
$search = '';
$status_filter = '';
$document_stats = array(
    'total' => 0,
    'pending' => 0,
    'approved' => 0,
    'rejected' => 0,
    'completed' => 0,
    'paid' => 0
);
$status_counts = [];
$result = [];
$table_exists = false;  // Initialize table_exists

// Page title
$page_title = "Document Requests - Barangay Management System";

// Process queries only if no DB error
if (!$db_error) {
    try {
        // Check if document_requests table exists
        $tables_result = $conn->query("SHOW TABLES LIKE 'document_requests'");
        if ($tables_result && $tables_result->rowCount() > 0) {
            $table_exists = true;
        }

        if ($table_exists) {
            // Process search
            $search = isset($_GET['search']) ? trim($_GET['search']) : '';
            $search_condition = '';
            $params = [];

            if (!empty($search)) {
                $search_condition = " AND (
                    r.last_name LIKE :search_last OR
                    r.first_name LIKE :search_first OR
                    d.document_type LIKE :search_doc OR
                    d.purpose LIKE :search_purpose OR
                    d.reference_number LIKE :search_ref OR
                    d.status LIKE :search_status
                )";
                $search_param = "%$search%";
                $params['search_last'] = $search_param;
                $params['search_first'] = $search_param;
                $params['search_doc'] = $search_param;
                $params['search_purpose'] = $search_param;
                $params['search_ref'] = $search_param;
                $params['search_status'] = $search_param;
            }

            // Process status filter
            $status_filter = isset($_GET['status']) ? trim($_GET['status']) : '';
            $status_condition = '';
            if (!empty($status_filter)) {
                $status_condition = " AND d.status = :status";
                $params['status'] = $status_filter;
            }

            // Process payment status filter
            $payment_status_filter = isset($_GET['payment_status']) ? trim($_GET['payment_status']) : '';
            $payment_status_condition = '';
            if (!empty($payment_status_filter)) {
                $payment_status_condition = " AND d.payment_status = :payment_status";
                $params['payment_status'] = $payment_status_filter;
            }

            // Pagination
            $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
            $records_per_page = 10;
            $offset = ($page - 1) * $records_per_page;

            // Get total records
            $count_query = "SELECT COUNT(*) as total FROM document_requests d
                            JOIN residents r ON d.resident_id = r.resident_id
                            WHERE 1=1 $search_condition $status_condition $payment_status_condition";
            $count_stmt = $conn->prepare($count_query);
            foreach ($params as $key => $value) {
                $count_stmt->bindValue(":$key", $value);
            }
            $count_stmt->execute();
            $total_records = $count_stmt->fetchColumn();
            $total_pages = ceil($total_records / $records_per_page);

            // Get document requests list
            $query = "SELECT d.*, r.first_name, r.middle_name, r.last_name, r.contact_number,
                      o.position, CONCAT(ro.first_name, ' ', ro.last_name) as official_name
                      FROM document_requests d
                      JOIN residents r ON d.resident_id = r.resident_id
                      LEFT JOIN officials o ON d.processed_by = o.official_id
                      LEFT JOIN residents ro ON o.resident_id = ro.resident_id
                      WHERE 1=1 $search_condition $status_condition $payment_status_condition
                      ORDER BY d.request_date DESC
                      LIMIT :offset, :per_page";

            try {
                $stmt = $conn->prepare($query);
                foreach ($params as $key => $value) {
                    $stmt->bindValue(":$key", $value);
                }
                $stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
                $stmt->bindParam(':per_page', $records_per_page, PDO::PARAM_INT);
                $stmt->execute();
                $result = $stmt->fetchAll(PDO::FETCH_ASSOC);

                // Ensure $result is an array
                if (!is_array($result)) {
                    $result = [];
                }
            } catch (PDOException $e) {
                error_log("Error fetching document requests: " . $e->getMessage());
                $result = [];
                $_SESSION['error'] = "A database error occurred while fetching documents.";
            }

            // Get status counts
            try {
                $status_query = "SELECT status, COUNT(*) as count FROM document_requests GROUP BY status";
                $status_stmt = $conn->prepare($status_query);
                $status_stmt->execute();
                $status_counts = [];
                while ($row = $status_stmt->fetch(PDO::FETCH_ASSOC)) {
                    $status_counts[$row['status']] = $row['count'];
                }
            } catch (PDOException $e) {
                error_log("Error fetching status counts: " . $e->getMessage());
                $status_counts = [];
            }

            // Get document statistics with comprehensive status mapping
            try {
                $stats_query = "SELECT
                                COUNT(*) as total,
                                SUM(CASE WHEN status IN ('Pending') THEN 1 ELSE 0 END) as pending,
                                SUM(CASE WHEN status IN ('Approved', 'Processing', 'Ready for Pickup') THEN 1 ELSE 0 END) as approved,
                                SUM(CASE WHEN status IN ('Rejected', 'Cancelled') THEN 1 ELSE 0 END) as rejected,
                                SUM(CASE WHEN status IN ('Released', 'Completed') THEN 1 ELSE 0 END) as completed,
                                SUM(CASE WHEN payment_status = 'Paid' THEN 1 ELSE 0 END) as paid
                                FROM document_requests";
                $stats_stmt = $conn->prepare($stats_query);
                $stats_stmt->execute();
                $document_stats = $stats_stmt->fetch(PDO::FETCH_ASSOC);

                // Ensure all values are integers and handle null values
                $document_stats = [
                    'total' => (int)($document_stats['total'] ?? 0),
                    'pending' => (int)($document_stats['pending'] ?? 0),
                    'approved' => (int)($document_stats['approved'] ?? 0),
                    'rejected' => (int)($document_stats['rejected'] ?? 0),
                    'completed' => (int)($document_stats['completed'] ?? 0),
                    'paid' => (int)($document_stats['paid'] ?? 0)
                ];

                // Debug logging (can be removed in production)
                error_log("Document Statistics: " . json_encode($document_stats));

            } catch (PDOException $e) {
                error_log("Error fetching document statistics: " . $e->getMessage());
                // Set default values if query fails
                $document_stats = [
                    'total' => 0,
                    'pending' => 0,
                    'approved' => 0,
                    'rejected' => 0,
                    'completed' => 0,
                    'paid' => 0
                ];
            }
        } else {
            $_SESSION['warning'] = "Document requests module is not set up yet. Please run <a href='create_table.php'>setup script</a> to create the table.";
        }
    } catch (PDOException $e) {
        // Log the error and show friendly message
        error_log("Database error: " . $e->getMessage());
        $_SESSION['error'] = "A database error occurred. Please try again later.";
        $table_exists = false;
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>

    <!-- Favicon -->
    <link rel="icon" href="<?php echo get_favicon_url($conn, '../../'); ?>" type="image/png">

    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="../../assets/css/style.css">
    <style>
        /* Table styles */
        .table-bordered-columns th,
        .table-bordered-columns td {
            border-right: 1px solid #dee2e6;
            border-bottom: 1px solid #dee2e6;
        }
        .table-bordered-columns th:last-child,
        .table-bordered-columns td:last-child {
            border-right: none;
        }
        .table-bordered-columns {
            border-left: 1px solid #dee2e6;
            border-right: 1px solid #dee2e6;
            border-top: 1px solid #dee2e6;
        }
        .table-bordered-columns thead th {
            border-bottom: 2px solid #4e73df;
            background-color: #f8f9fc;
        }

        /* Action buttons styling */
        .action-buttons {
            display: flex;
            justify-content: center;
            gap: 5px;
        }

        .action-column {
            text-align: center;
            min-width: 120px;
            white-space: nowrap;
        }

        .btn-action {
            width: 36px;
            height: 36px;
            padding: 6px;
            border-radius: 5px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            transition: transform 0.2s ease;
        }

        .btn-action:hover {
            transform: scale(1.1);
        }

        /* Stat Card Styles */
        .stat-card {
            border-radius: 0.75rem;
            overflow: hidden;
            height: 100%;
            transition: all 0.3s ease;
            border: none;
            box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
            position: relative;
            z-index: 1;
        }
        .stat-card:hover {
            transform: translateY(-5px) !important;
            box-shadow: 0 0.5rem 2rem 0 rgba(58, 59, 69, 0.2) !important;
            z-index: 2;
            cursor: pointer;
        }
        .stat-card:hover .stat-icon {
            transform: scale(1.1);
        }
        .stat-icon {
            font-size: 2.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            height: 60px;
            width: 60px;
            border-radius: 50%;
            transition: transform 0.3s ease;
        }

        /* Border colors for different statuses */
        .border-primary { border-left: 4px solid #4e73df !important; }
        .border-success { border-left: 4px solid #1cc88a !important; }
        .border-info { border-left: 4px solid #36b9cc !important; }
        .border-warning { border-left: 4px solid #f6c23e !important; }
        .border-danger { border-left: 4px solid #e74a3b !important; }

        /* Background colors with opacity */
        .bg-primary-soft { background-color: rgba(78, 115, 223, 0.1) !important; }
        .bg-success-soft { background-color: rgba(28, 200, 138, 0.1) !important; }
        .bg-info-soft { background-color: rgba(54, 185, 204, 0.1) !important; }
        .bg-warning-soft { background-color: rgba(246, 194, 62, 0.1) !important; }
        .bg-danger-soft { background-color: rgba(231, 74, 59, 0.1) !important; }
        .bg-secondary { background-color: #6c757d !important; }

        /* Special gradient card styles */
        .stat-card.gradient-success {
            background: linear-gradient(135deg, #1cc88a 0%, #17a673 100%);
            color: white;
        }
        .stat-card.gradient-success:hover {
            background: linear-gradient(135deg, #17a673 0%, #138f5f 100%);
        }

        /* Text color utilities */
        .text-white-50 { color: rgba(255, 255, 255, 0.5) !important; }

        /* Card hover effects */
        .card.shadow {
            transition: all 0.3s ease;
        }
        .card.shadow:hover {
            box-shadow: 0 0.5rem 1.5rem 0 rgba(58, 59, 69, 0.15) !important;
            transform: translateY(-3px);
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <?php include '../../includes/sidebar.php'; ?>

            <!-- Main Content -->
            <main class="col-md-8 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">📄 Document Requests</h1>
                    <div class="btn-toolbar">
                        <?php if (!$db_error && hasPermission('add_document')): ?>
                        <a href="add_document.php" class="btn btn-primary">
                            ➕ Add New Request
                        </a>
                        <?php endif; ?>
                    </div>
                </div>

                <?php if ($db_error): ?>
                <!-- Database Error Message -->
                <div class="alert alert-danger">
                    ❌ <strong>Database Connection Error:</strong> Could not connect to the database. Please contact the system administrator.
                </div>
                <?php else: ?>

                <?php if (isset($_SESSION['success'])): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    ✅ <?php
                    echo $_SESSION['success'];
                    unset($_SESSION['success']);
                    ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php endif; ?>

                <?php if (isset($_SESSION['error'])): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    ❌ <?php
                    echo $_SESSION['error'];
                    unset($_SESSION['error']);
                    ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php endif; ?>

                <?php if (isset($_SESSION['warning'])): ?>
                <div class="alert alert-warning alert-dismissible fade show" role="alert">
                    ⚠️ <?php
                    echo $_SESSION['warning'];
                    unset($_SESSION['warning']);
                    ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php endif; ?>

                <!-- Quick Statistics -->
                <div class="row mb-4">
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card stat-card border-primary">
                            <div class="card-body p-3">
                                <div class="row align-items-center">
                                    <div class="col-4">
                                        <div class="stat-icon bg-primary-soft text-primary">
                                            📋
                                        </div>
                                    </div>
                                    <div class="col-8 text-end">
                                        <h4 class="mt-0 mb-1" id="total-count"><?php echo number_format($document_stats['total'] ?? 0); ?></h4>
                                        <p class="mb-0 text-muted">Total Requests</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card stat-card border-warning">
                            <div class="card-body p-3">
                                <div class="row align-items-center">
                                    <div class="col-4">
                                        <div class="stat-icon bg-warning-soft text-warning">
                                            ⏳
                                        </div>
                                    </div>
                                    <div class="col-8 text-end">
                                        <h4 class="mt-0 mb-1" id="pending-count"><?php echo number_format($document_stats['pending'] ?? 0); ?></h4>
                                        <p class="mb-0 text-muted">Pending</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card stat-card border-success">
                            <div class="card-body p-3">
                                <div class="row align-items-center">
                                    <div class="col-4">
                                        <div class="stat-icon bg-success-soft text-success">
                                            ✅
                                        </div>
                                    </div>
                                    <div class="col-8 text-end">
                                        <h4 class="mt-0 mb-1" id="approved-count"><?php echo number_format($document_stats['approved'] ?? 0); ?></h4>
                                        <p class="mb-0 text-muted">Approved</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card stat-card border-info">
                            <div class="card-body p-3">
                                <div class="row align-items-center">
                                    <div class="col-4">
                                        <div class="stat-icon bg-info-soft text-info">
                                            📄
                                        </div>
                                    </div>
                                    <div class="col-8 text-end">
                                        <h4 class="mt-0 mb-1" id="completed-count"><?php echo number_format($document_stats['completed'] ?? 0); ?></h4>
                                        <p class="mb-0 text-muted">Completed</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Second Row - Payment Statistics -->
                <div class="row mb-4">
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card stat-card border-success" style="background: linear-gradient(135deg, #1cc88a 0%, #17a673 100%);">
                            <div class="card-body p-3">
                                <div class="row align-items-center">
                                    <div class="col-4">
                                        <div class="stat-icon text-white" style="background: rgba(255,255,255,0.2);">
                                            💰
                                        </div>
                                    </div>
                                    <div class="col-8 text-end">
                                        <h4 class="mt-0 mb-1 text-white" id="paid-count"><?php echo number_format($document_stats['paid'] ?? 0); ?></h4>
                                        <p class="mb-0 text-white-50">Paid Documents</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card stat-card border-danger">
                            <div class="card-body p-3">
                                <div class="row align-items-center">
                                    <div class="col-4">
                                        <div class="stat-icon bg-danger-soft text-danger">
                                            💳
                                        </div>
                                    </div>
                                    <div class="col-8 text-end">
                                        <h4 class="mt-0 mb-1" id="unpaid-count"><?php echo number_format(($document_stats['total'] ?? 0) - ($document_stats['paid'] ?? 0)); ?></h4>
                                        <p class="mb-0 text-muted">Unpaid Documents</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card stat-card border-secondary">
                            <div class="card-body p-3">
                                <div class="row align-items-center">
                                    <div class="col-4">
                                        <div class="stat-icon bg-secondary text-white">
                                            📊
                                        </div>
                                    </div>
                                    <div class="col-8 text-end">
                                        <h4 class="mt-0 mb-1" id="payment-rate"><?php
                                            $total = $document_stats['total'] ?? 0;
                                            $paid = $document_stats['paid'] ?? 0;
                                            $rate = $total > 0 ? round(($paid / $total) * 100, 1) : 0;
                                            echo $rate . '%';
                                        ?></h4>
                                        <p class="mb-0 text-muted">Payment Rate</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card stat-card border-warning">
                            <div class="card-body p-3">
                                <div class="row align-items-center">
                                    <div class="col-4">
                                        <div class="stat-icon bg-warning-soft text-warning">
                                            ❌
                                        </div>
                                    </div>
                                    <div class="col-8 text-end">
                                        <h4 class="mt-0 mb-1" id="rejected-count"><?php echo number_format($document_stats['rejected'] ?? 0); ?></h4>
                                        <p class="mb-0 text-muted">Rejected</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Search and Filter -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">🔍 Search and Filter</h6>
                    </div>
                    <div class="card-body">
                        <form method="GET" class="row g-3">
                            <div class="col-md-6">
                                <label for="search" class="form-label">Search</label>
                                <input type="text" class="form-control" id="search" name="search" placeholder="Search by name, document type, purpose..." value="<?php echo htmlspecialchars($search); ?>">
                            </div>
                            <div class="col-md-3">
                                <label for="status" class="form-label">Document Status</label>
                                <select class="form-select" id="status" name="status">
                                    <option value="">All Status</option>
                                    <option value="Pending" <?php echo ($status_filter == 'Pending') ? 'selected' : ''; ?>>Pending</option>
                                    <option value="Approved" <?php echo ($status_filter == 'Approved') ? 'selected' : ''; ?>>Approved</option>
                                    <option value="Processing" <?php echo ($status_filter == 'Processing') ? 'selected' : ''; ?>>Processing</option>
                                    <option value="Ready for Pickup" <?php echo ($status_filter == 'Ready for Pickup') ? 'selected' : ''; ?>>Ready for Pickup</option>
                                    <option value="Released" <?php echo ($status_filter == 'Released') ? 'selected' : ''; ?>>Released</option>
                                    <option value="Completed" <?php echo ($status_filter == 'Completed') ? 'selected' : ''; ?>>Completed</option>
                                    <option value="Rejected" <?php echo ($status_filter == 'Rejected') ? 'selected' : ''; ?>>Rejected</option>
                                    <option value="Cancelled" <?php echo ($status_filter == 'Cancelled') ? 'selected' : ''; ?>>Cancelled</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="payment_status" class="form-label">Payment Status</label>
                                <select class="form-select" id="payment_status" name="payment_status">
                                    <option value="">All Payments</option>
                                    <option value="Paid" <?php echo (isset($_GET['payment_status']) && $_GET['payment_status'] == 'Paid') ? 'selected' : ''; ?>>💰 Paid</option>
                                    <option value="Unpaid" <?php echo (isset($_GET['payment_status']) && $_GET['payment_status'] == 'Unpaid') ? 'selected' : ''; ?>>💳 Unpaid</option>
                                    <option value="Waived" <?php echo (isset($_GET['payment_status']) && $_GET['payment_status'] == 'Waived') ? 'selected' : ''; ?>>🎁 Waived</option>
                                    <option value="Free" <?php echo (isset($_GET['payment_status']) && $_GET['payment_status'] == 'Free') ? 'selected' : ''; ?>>🆓 Free</option>
                                </select>
                            </div>
                            <div class="col-12">
                                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                    <button type="submit" class="btn btn-primary me-md-2">🔍 Filter</button>
                                    <a href="?" class="btn btn-secondary">🔄 Reset</a>
                                    <button type="button" class="btn btn-success" onclick="exportToExcel()">
                                        📊 Export
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Document Requests Table -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                        <h6 class="m-0 font-weight-bold text-primary">📃 Document Requests</h6>
                        <div class="dropdown no-arrow">
                            <a class="dropdown-toggle" href="#" role="button" id="dropdownMenuLink" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-ellipsis-v fa-sm fa-fw text-gray-400"></i>
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end shadow" aria-labelledby="dropdownMenuLink">
                                <li><h6 class="dropdown-header">📋 Document Status</h6></li>
                                <li><a class="dropdown-item" href="?status=Pending">⏳ View Pending</a></li>
                                <li><a class="dropdown-item" href="?status=Approved">✅ View Approved</a></li>
                                <li><a class="dropdown-item" href="?status=Completed">📄 View Completed</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><h6 class="dropdown-header">💰 Payment Status</h6></li>
                                <li><a class="dropdown-item" href="?payment_status=Paid">💰 View Paid</a></li>
                                <li><a class="dropdown-item" href="?payment_status=Unpaid">💳 View Unpaid</a></li>
                                <li><a class="dropdown-item" href="?payment_status=Waived">🎁 View Waived</a></li>
                                <li><a class="dropdown-item" href="?payment_status=Free">🆓 View Free</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="#" onclick="window.print(); return false;">🖨️ Print List</a></li>
                            </ul>
                        </div>
                    </div>
                    <div class="card-body">
                        <?php if (!$table_exists): ?>
                            <div class="alert alert-info mb-0">
                                ℹ️ The document requests module is still being set up. Please <a href="create_table.php">run the setup script</a> to create the necessary tables.
                            </div>
                        <?php elseif (empty($result)): ?>
                            <div class="alert alert-info mb-0">
                                ℹ️ No document requests found matching your criteria.
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover table-bordered-columns">
                                    <thead class="table-light">
                                        <tr>
                                            <th>#</th>
                                            <th>👤 Resident</th>
                                            <th>📝 Document Type</th>
                                            <th>📅 Request Date</th>
                                            <th>📅 Release Date</th>
                                            <th>📅 Expiry Date</th>
                                            <th>🚦 Status</th>
                                            <th>💵 Payment</th>
                                            <th>👮 Processed By</th>
                                            <th class="action-column">📄 Document File</th>
                                            <th class="action-column">⚙️ Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($result as $row): ?>
                                        <tr>
                                            <td><?php echo $row['request_id']; ?></td>
                                            <td><?php echo htmlspecialchars($row['last_name'] . ', ' . $row['first_name'] . ' ' . $row['middle_name']); ?></td>
                                            <td>
                                                <?php
                                                // Add document type icon based on document type
                                                $docTypeIcon = '📄';
                                                if ($row['document_type'] == 'Barangay Clearance') $docTypeIcon = '🔍';
                                                else if ($row['document_type'] == 'Certificate of Residency') $docTypeIcon = '🏠';
                                                else if ($row['document_type'] == 'Certificate of Indigency') $docTypeIcon = '📑';
                                                else if ($row['document_type'] == 'Barangay Certificate') $docTypeIcon = '📜';
                                                else if ($row['document_type'] == 'Certificate of Good Standing') $docTypeIcon = '🏅';
                                                else if ($row['document_type'] == 'Certificate of No Pending Case') $docTypeIcon = '⚖️';
                                                echo $docTypeIcon . ' ' . htmlspecialchars($row['document_type']);
                                                ?>
                                            </td>
                                            <td><?php echo date('M d, Y', strtotime($row['request_date'])); ?></td>
                                            <td><?php echo $row['release_date'] ? date('M d, Y', strtotime($row['release_date'])) : 'Not released'; ?></td>
                                            <td><?php echo $row['expiry_date'] ? date('M d, Y', strtotime($row['expiry_date'])) : 'No expiry'; ?></td>
                                            <td>
                                                <span class="badge <?php
                                                    echo ($row['status'] == 'Pending') ? 'bg-warning' :
                                                        (($row['status'] == 'Processing') ? 'bg-info' :
                                                        (($row['status'] == 'Ready for Pickup') ? 'bg-primary' :
                                                        (($row['status'] == 'Released' || $row['status'] == 'Completed') ? 'bg-success' :
                                                        (($row['status'] == 'Rejected' || $row['status'] == 'Cancelled') ? 'bg-danger' : 'bg-secondary'))));
                                                ?>">
                                                    <?php
                                                    $statusIcon = '';
                                                    if ($row['status'] == 'Pending') $statusIcon = '⏳ ';
                                                    else if ($row['status'] == 'Processing') $statusIcon = '🔄 ';
                                                    else if ($row['status'] == 'Ready for Pickup') $statusIcon = '📦 ';
                                                    else if ($row['status'] == 'Released') $statusIcon = '✅ ';
                                                    else if ($row['status'] == 'Completed') $statusIcon = '✅ ';
                                                    else if ($row['status'] == 'Rejected') $statusIcon = '❌ ';
                                                    else if ($row['status'] == 'Cancelled') $statusIcon = '🚫 ';
                                                    echo $statusIcon . $row['status'];
                                                    ?>
                                                </span>
                                            </td>
                                            <td>
                                                <?php
                                                // Enhanced payment status display with proper colors and icons
                                                $paymentBadgeClass = 'bg-secondary';
                                                $paymentIcon = '💸';

                                                switch($row['payment_status']) {
                                                    case 'Paid':
                                                        $paymentBadgeClass = 'bg-success';
                                                        $paymentIcon = '💰';
                                                        break;
                                                    case 'Unpaid':
                                                        $paymentBadgeClass = 'bg-danger';
                                                        $paymentIcon = '💳';
                                                        break;
                                                    case 'Waived':
                                                        $paymentBadgeClass = 'bg-info';
                                                        $paymentIcon = '🎁';
                                                        break;
                                                    case 'Free':
                                                        $paymentBadgeClass = 'bg-primary';
                                                        $paymentIcon = '🆓';
                                                        break;
                                                }
                                                ?>
                                                <span class="badge <?php echo $paymentBadgeClass; ?>">
                                                    <?php echo $paymentIcon . ' ' . $row['payment_status']; ?>
                                                </span>
                                                <?php if ($row['payment_amount'] > 0): ?>
                                                <br><span class="small text-muted">₱<?php echo number_format($row['payment_amount'], 2); ?></span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php
                                                // Get the official/processor name
                                                if (!empty($row['processed_by'])) {
                                                    if (!empty($row['official_name'])) {
                                                        echo htmlspecialchars($row['official_name']) .
                                                            ($row['position'] ? ' (' . htmlspecialchars($row['position']) . ')' : '');
                                                    } else {
                                                        echo 'ID: ' . $row['processed_by'];
                                                    }
                                                } else {
                                                    echo 'Not assigned';
                                                }
                                                ?>
                                            </td>
                                            <td>
                                                <div class="action-buttons">
                                                <?php if (!empty($row['document_file'])): ?>
                                                    <a href="../../uploads/documents/<?php echo $row['document_file']; ?>" target="_blank" class="btn btn-sm btn-info" data-bs-toggle="tooltip" title="View Document">
                                                        <i class="fas fa-file-alt"></i>
                                                    </a>
                                                <?php endif; ?>

                                                <?php if (in_array($row['status'], ['Ready for Pickup', 'Released']) && $row['payment_status'] == 'Paid'): ?>
                                                <?php
                                                    // Determine the print URL based on document type
                                                    $printUrl = '#';
                                                    switch($row['document_type']) {
                                                        case 'Barangay Clearance':
                                                            $printUrl = "clearance.php?id=" . $row['request_id'];
                                                            break;
                                                        case 'Certificate of Residency':
                                                            $printUrl = "residency.php?id=" . $row['request_id'];
                                                            break;
                                                        case 'Certificate of Indigency':
                                                            $printUrl = "indigency.php?id=" . $row['request_id'];
                                                            break;
                                                        case 'Barangay Certificate':
                                                            $printUrl = "certificate.php?id=" . $row['request_id'];
                                                            break;
                                                        case 'Certificate of Good Standing':
                                                            $printUrl = "good_standing.php?id=" . $row['request_id'];
                                                            break;
                                                        case 'Certificate of No Pending Case':
                                                            $printUrl = "no_pending_case.php?id=" . $row['request_id'];
                                                            break;
                                                        case 'Certificate of Demolition':
                                                            $printUrl = "demolition.php?id=" . $row['request_id'];
                                                            break;
                                                        case 'Solo Parents':
                                                            $printUrl = "solo_parents.php?id=" . $row['request_id'];
                                                            break;
                                                        case 'TODA Certificate':
                                                            $printUrl = "toda_certificate.php?id=" . $row['request_id'];
                                                            break;
                                                        case 'Business Permit':
                                                            $printUrl = "business_permit.php?id=" . $row['request_id'];
                                                            break;
                                                        default:
                                                            $printUrl = "view_document.php?id=" . $row['request_id'] . "&print=1";
                                                    }
                                                ?>
                                                <a href="<?php echo $printUrl; ?>" class="btn btn-sm btn-success" data-bs-toggle="tooltip" title="Print Document">
                                                    <i class="fas fa-print"></i>
                                                </a>
                                                <?php endif; ?>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="action-buttons">
                                                    <a href="view_document.php?id=<?php echo $row['request_id']; ?>" class="btn btn-sm btn-info" data-bs-toggle="tooltip" title="View Document">
                                                        <i class="fas fa-file-alt"></i>
                                                    </a>
                                                    <?php if (hasPermission('edit_document') && $row['status'] != 'Released' && $row['status'] != 'Completed'): ?>
                                                    <a href="edit_document.php?id=<?php echo $row['request_id']; ?>" class="btn btn-sm btn-primary" data-bs-toggle="tooltip" title="Edit Document">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <?php endif; ?>

                                                    <?php if (hasPermission('delete_document') && $row['status'] == 'Pending'): ?>
                                                    <button type="button" class="btn btn-sm btn-danger delete-btn" data-bs-toggle="tooltip" title="Delete Document" data-bs-target="#confirmDeleteModal" data-id="<?php echo $row['request_id']; ?>">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>

                            <!-- Pagination -->
                            <?php if (isset($total_pages) && $total_pages > 1): ?>
                            <div class="d-flex justify-content-center mt-4">
                                <nav aria-label="Page navigation">
                                    <ul class="pagination">
                                        <li class="page-item <?php echo ($page <= 1) ? 'disabled' : ''; ?>">
                                            <a class="page-link" href="?page=<?php echo $page-1; ?><?php echo !empty($search) ? '&search=' . urlencode($search) : ''; ?><?php echo !empty($status_filter) ? '&status=' . urlencode($status_filter) : ''; ?>" aria-label="Previous">
                                                <span aria-hidden="true">&laquo;</span>
                                            </a>
                                        </li>

                                        <?php for ($i = 1; $i <= $total_pages; $i++): ?>
                                            <?php if ($i == 1 || $i == $total_pages || ($i >= $page - 1 && $i <= $page + 1)): ?>
                                                <li class="page-item <?php echo ($i == $page) ? 'active' : ''; ?>">
                                                    <a class="page-link" href="?page=<?php echo $i; ?><?php echo !empty($search) ? '&search=' . urlencode($search) : ''; ?><?php echo !empty($status_filter) ? '&status=' . urlencode($status_filter) : ''; ?>">
                                                        <?php echo $i; ?>
                                                    </a>
                                                </li>
                                            <?php elseif ($i == 2 || $i == $total_pages - 1): ?>
                                                <li class="page-item disabled">
                                                    <a class="page-link" href="#">...</a>
                                                </li>
                                            <?php endif; ?>
                                        <?php endfor; ?>

                                        <li class="page-item <?php echo ($page >= $total_pages) ? 'disabled' : ''; ?>">
                                            <a class="page-link" href="?page=<?php echo $page+1; ?><?php echo !empty($search) ? '&search=' . urlencode($search) : ''; ?><?php echo !empty($status_filter) ? '&status=' . urlencode($status_filter) : ''; ?>" aria-label="Next">
                                                <span aria-hidden="true">&raquo;</span>
                                            </a>
                                        </li>
                                    </ul>
                                </nav>
                            </div>
                            <?php endif; ?>
                        <?php endif; ?>
                    </div>
                </div>
                <?php endif; ?>
            </main>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="confirmDeleteModal" tabindex="-1" aria-labelledby="confirmDeleteModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title" id="confirmDeleteModalLabel">❌ Confirm Delete</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>Are you sure you want to delete this document request? This action cannot be undone.</p>
                    <p><strong>Note:</strong> This will permanently remove the document request from the system.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <form action="delete_document.php" method="POST">
                        <input type="hidden" name="request_id" id="delete_request_id">
                        <button type="submit" class="btn btn-danger">Delete Document</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Initialize tooltips
        document.addEventListener('DOMContentLoaded', function() {
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
            var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl)
            });

            // Handle delete button click
            const deleteButtons = document.querySelectorAll('.delete-btn');
            deleteButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const requestId = this.getAttribute('data-id');
                    document.getElementById('delete_request_id').value = requestId;
                    const modal = new bootstrap.Modal(document.getElementById('confirmDeleteModal'));
                    modal.show();
                });
            });

            // Function to refresh statistics
            function refreshStatistics() {
                fetch('get_document_stats.php')
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            // Update the statistics cards using specific IDs
                            const stats = data.stats;
                            const totalElement = document.getElementById('total-count');
                            const pendingElement = document.getElementById('pending-count');
                            const approvedElement = document.getElementById('approved-count');
                            const completedElement = document.getElementById('completed-count');

                            if (totalElement) totalElement.textContent = stats.total.toLocaleString();
                            if (pendingElement) pendingElement.textContent = stats.pending.toLocaleString();
                            if (approvedElement) approvedElement.textContent = stats.approved.toLocaleString();
                            if (completedElement) completedElement.textContent = stats.completed.toLocaleString();

                            console.log('Statistics updated:', stats);
                        }
                    })
                    .catch(error => {
                        console.error('Error refreshing statistics:', error);
                    });
            }

            // Refresh statistics every 30 seconds
            setInterval(refreshStatistics, 30000);

            // Refresh statistics when page becomes visible (user switches back to tab)
            document.addEventListener('visibilitychange', function() {
                if (!document.hidden) {
                    refreshStatistics();
                }
            });

            // Function to show toast notifications
            function showToast(message, type = 'success') {
                // Create toast element
                const toastContainer = document.createElement('div');
                toastContainer.className = 'toast-container position-fixed bottom-0 end-0 p-3';
                toastContainer.style.zIndex = '11';

                const toast = document.createElement('div');
                toast.className = `toast align-items-center text-white bg-${type} border-0`;
                toast.setAttribute('role', 'alert');
                toast.setAttribute('aria-live', 'assertive');
                toast.setAttribute('aria-atomic', 'true');

                // Set icon based on type
                let icon = '📝'; // default/info
                if (type === 'success') icon = '✅';
                else if (type === 'danger') icon = '❌';
                else if (type === 'warning') icon = '⚠️';

                const toastContent = `
                    <div class="d-flex">
                        <div class="toast-body">
                            ${icon} ${message}
                        </div>
                        <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
                    </div>
                `;

                toast.innerHTML = toastContent;
                toastContainer.appendChild(toast);
                document.body.appendChild(toastContainer);

                // Initialize and show toast
                const bsToast = new bootstrap.Toast(toast, {
                    autohide: true,
                    delay: 5000
                });
                bsToast.show();

                // Remove container when toast is hidden
                toast.addEventListener('hidden.bs.toast', function() {
                    document.body.removeChild(toastContainer);
                });
            }

            // Handle session messages
            <?php if (isset($_SESSION['success'])): ?>
                showToast("<?php echo addslashes($_SESSION['success']); ?>", "success");
                <?php unset($_SESSION['success']); ?>
            <?php endif; ?>

            <?php if (isset($_SESSION['error'])): ?>
                showToast("<?php echo addslashes($_SESSION['error']); ?>", "danger");
                <?php unset($_SESSION['error']); ?>
            <?php endif; ?>

            <?php if (isset($_SESSION['warning'])): ?>
                showToast("<?php echo addslashes($_SESSION['warning']); ?>", "warning");
                <?php unset($_SESSION['warning']); ?>
            <?php endif; ?>

            <?php if (isset($_SESSION['info'])): ?>
                showToast("<?php echo addslashes($_SESSION['info']); ?>", "info");
                <?php unset($_SESSION['info']); ?>
            <?php endif; ?>
        });

        function exportToExcel() {
            alert('Export to Excel functionality will be implemented soon');
        }

        // Add click handlers for quick filter cards
        document.addEventListener('DOMContentLoaded', function() {
            document.addEventListener('click', function(e) {
                if (e.target.closest('.stat-card')) {
                    const card = e.target.closest('.stat-card');
                    const cardText = card.querySelector('p').textContent.trim();

                    // Quick filter based on card clicked
                    if (cardText === 'Paid Documents') {
                        window.location.href = '?payment_status=Paid';
                    } else if (cardText === 'Unpaid Documents') {
                        window.location.href = '?payment_status=Unpaid';
                    } else if (cardText === 'Pending') {
                        window.location.href = '?status=Pending';
                    } else if (cardText === 'Approved') {
                        window.location.href = '?status=Approved';
                    } else if (cardText === 'Completed') {
                        window.location.href = '?status=Completed';
                    } else if (cardText === 'Rejected') {
                        window.location.href = '?status=Rejected';
                    }
                }
            });

            // Add hover effects to stat cards
            document.querySelectorAll('.stat-card').forEach(card => {
                card.style.cursor = 'pointer';
                card.style.transition = 'all 0.3s ease';
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-2px)';
                    this.style.boxShadow = '0 4px 8px rgba(0,0,0,0.15)';
                });
                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                    this.style.boxShadow = '0 1px 3px rgba(0,0,0,0.12)';
                });
            });
        });
    </script>
</body>
</html>